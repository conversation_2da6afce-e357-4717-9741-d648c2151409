# 🔧 Fix EmailJS Template - Name & Email Not Showing

## 🚨 URGENT: Your EmailJS template is missing the correct variables!

### Step 1: Go to EmailJS Dashboard
1. Open https://www.emailjs.com/
2. Login to your account
3. Click **"Email Templates"** in the sidebar
4. Find template: **template_gjtmaxv**
5. Click **"Edit"**

### Step 2: Replace Your Current Template

**DELETE everything in your current template and paste this:**

```html
Subject: New Portfolio Contact: {{subject}}

📧 NEW CONTACT FROM PORTFOLIO

👤 Name: {{from_name}}
📧 Email: {{from_email}}
📝 Subject: {{subject}}
🕒 Time: {{timestamp}}

💬 Message:
{{message}}

---
✅ Sent from portfolio contact form
🔄 Reply to: {{reply_to}}
📱 Contact type: {{contact_type}}
```

### Step 3: Save Template
1. Click **"Save"** button
2. Wait for confirmation message
3. Template should now be updated

### Step 4: Test Immediately
1. Go to your portfolio website
2. Fill out contact form with:
   - **Name:** Test User
   - **Email:** <EMAIL>
   - **Subject:** Template Test
   - **Message:** Testing if name and email show up now
3. Submit form
4. Check your email: **<EMAIL>**

### Step 5: Verify Results
You should now see:
- ✅ Name: Test User
- ✅ Email: <EMAIL>
- ✅ Subject: Template Test
- ✅ Message: Testing if name and email show up now

## 🎨 Enhanced Template (Optional)

If you want a prettier email, use this HTML template instead:

```html
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; }
        .header { background: #4f46e5; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9fafb; }
        .field { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 New Portfolio Contact</h1>
    </div>
    <div class="content">
        <div class="field"><strong>👤 Name:</strong> {{from_name}}</div>
        <div class="field"><strong>📧 Email:</strong> {{from_email}}</div>
        <div class="field"><strong>📝 Subject:</strong> {{subject}}</div>
        <div class="field"><strong>🕒 Time:</strong> {{timestamp}}</div>
        <div class="field">
            <strong>💬 Message:</strong><br>
            {{message}}
        </div>
        <p><strong>Reply to:</strong> {{reply_to}}</p>
    </div>
</body>
</html>
```

## 🔍 Troubleshooting

### Problem: Still not working?
1. **Clear browser cache**
2. **Wait 5-10 minutes** for EmailJS to update
3. **Try different variable names:**
   - Instead of `{{from_name}}` try `{{name}}`
   - Instead of `{{from_email}}` try `{{email}}`

### Problem: Variables show as {{from_name}} in email?
- Make sure you're using **double curly braces**: `{{from_name}}`
- NOT single: `{from_name}` or triple: `{{{from_name}}}`

### Problem: No email received?
1. Check **spam folder**
2. Verify email: **<EMAIL>**
3. Check EmailJS dashboard for error logs

## 📋 Quick Checklist
- [ ] Logged into EmailJS dashboard
- [ ] Found template: template_gjtmaxv
- [ ] Replaced template content with new version
- [ ] Saved template successfully
- [ ] Tested contact form
- [ ] Received email with name and email visible
- [ ] All fields populated correctly

## 🆘 Emergency Fallback

If EmailJS template still doesn't work, the code now includes ALL contact details in the message body as a fallback. So even if the template variables fail, you'll still get:

```
📧 NEW PORTFOLIO CONTACT 📧

👤 NAME: [User's Name]
📧 EMAIL: [User's Email]
📝 SUBJECT: [Subject]
🕒 TIME: [Timestamp]

💬 MESSAGE:
[User's Message]
```

This ensures you NEVER miss contact information again!

## 🎯 Expected Result

After fixing the template, your emails should look like:

```
Subject: New Portfolio Contact: Hello from John

📧 NEW CONTACT FROM PORTFOLIO

👤 Name: John Doe
📧 Email: <EMAIL>
📝 Subject: Hello from John
🕒 Time: 12/30/2024, 3:45:23 PM

💬 Message:
Hi Akshay! I saw your portfolio and would love to discuss a project opportunity.

---
✅ Sent from portfolio contact form
🔄 Reply to: <EMAIL>
📱 Contact type: Portfolio Website Contact
```

**The name and email will now be clearly visible!** 🎉
