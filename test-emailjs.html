<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ffa500;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #333;
            background: #2a2a2a;
            color: white;
            border-radius: 5px;
        }
        button {
            background: #ffa500;
            color: black;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #ff8c00;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #2d5a2d;
            border: 1px solid #4caf50;
        }
        .error {
            background: #5a2d2d;
            border: 1px solid #f44336;
        }
        .config {
            background: #2d2d5a;
            border: 1px solid #2196f3;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>EmailJS Configuration Test</h1>
    
    <div class="config">
        <h3>Current Configuration:</h3>
        <p><strong>Service ID:</strong> <span id="serviceId">Loading...</span></p>
        <p><strong>Template ID:</strong> <span id="templateId">Loading...</span></p>
        <p><strong>Public Key:</strong> <span id="publicKey">Loading...</span></p>
    </div>

    <form id="testForm">
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" value="Test User" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="subject">Subject:</label>
            <input type="text" id="subject" value="EmailJS Test Message">
        </div>
        
        <div class="form-group">
            <label for="message">Message:</label>
            <textarea id="message" rows="4" required>This is a test message to verify EmailJS configuration is working correctly.</textarea>
        </div>
        
        <button type="submit">Send Test Email</button>
    </form>

    <div id="status"></div>

    <script>
        // Load environment variables (simulated)
        const config = {
            serviceId: 'service_mshm1ku',
            templateId: 'template_gjtmaxv',
            publicKey: 'e8z9fkbuNRmd04-70'
        };

        // Display configuration
        document.getElementById('serviceId').textContent = config.serviceId || 'Not set';
        document.getElementById('templateId').textContent = config.templateId || 'Not set';
        document.getElementById('publicKey').textContent = config.publicKey || 'Not set';

        // Initialize EmailJS
        if (config.publicKey) {
            emailjs.init(config.publicKey);
        }

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<div class="status">Sending test email...</div>';

            try {
                // Check configuration
                if (!config.serviceId || !config.templateId || !config.publicKey) {
                    throw new Error('EmailJS configuration incomplete');
                }

                // Prepare template parameters
                const templateParams = {
                    from_name: document.getElementById('name').value,
                    from_email: document.getElementById('email').value,
                    to_email: '<EMAIL>',
                    to_name: 'Akshay Juluri',
                    subject: document.getElementById('subject').value,
                    message: document.getElementById('message').value,
                    reply_to: document.getElementById('email').value,
                    timestamp: new Date().toLocaleString()
                };

                console.log('Sending with params:', templateParams);

                // Send email
                const result = await emailjs.send(
                    config.serviceId,
                    config.templateId,
                    templateParams
                );

                console.log('EmailJS result:', result);

                if (result.status === 200) {
                    statusDiv.innerHTML = '<div class="status success">✅ Test email sent successfully! Check <EMAIL></div>';
                } else {
                    throw new Error(`EmailJS returned status: ${result.status}`);
                }

            } catch (error) {
                console.error('EmailJS test failed:', error);
                statusDiv.innerHTML = `<div class="status error">❌ Test failed: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
