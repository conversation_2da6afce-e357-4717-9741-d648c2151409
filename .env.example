# Email Configuration (for backend server)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Port (optional)
PORT=3001

# EmailJS Configuration (for frontend direct email sending)
# Get these values from https://www.emailjs.com/
VITE_EMAILJS_SERVICE_ID=your_service_id_here
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here

# Instructions for EmailJS setup:
# 1. Copy this file to .env
# 2. Sign up at https://www.emailjs.com/
# 3. Create a service (Gmail, Outlook, etc.)
# 4. Create an email template with variables: from_name, from_email, subject, message
# 5. Get your public key from the integration section
# 6. Replace the placeholder values above with your actual EmailJS credentials
