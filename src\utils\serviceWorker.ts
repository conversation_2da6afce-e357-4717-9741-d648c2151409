// Service Worker registration and management

const isLocalhost = () => {
  if (typeof window === "undefined") return false;
  return Boolean(
    window.location.hostname === "localhost" ||
      window.location.hostname === "[::1]" ||
      window.location.hostname.match(
        /^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/
      )
  );
};

interface ServiceWorkerConfig {
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
  onOfflineReady?: () => void;
}

export function registerSW(config?: ServiceWorkerConfig) {
  if (typeof window === "undefined" || !("serviceWorker" in navigator)) {
    return;
  }

  const publicUrl = new URL(import.meta.env.BASE_URL, window.location.href);
  if (publicUrl.origin !== window.location.origin) {
    return;
  }

  window.addEventListener("load", () => {
    const swUrl = `${import.meta.env.BASE_URL}sw.js`;

    if (isLocalhost()) {
      checkValidServiceWorker(swUrl, config);
      navigator.serviceWorker.ready.then(() => {
        console.log(
          "This web app is being served cache-first by a service worker."
        );
      });
    } else {
      registerValidSW(swUrl, config);
    }
  });
}

function registerValidSW(swUrl: string, config?: ServiceWorkerConfig) {
  navigator.serviceWorker
    .register(swUrl)
    .then((registration) => {
      console.log("SW registered: ", registration);

      registration.onupdatefound = () => {
        const installingWorker = registration.installing;
        if (installingWorker == null) {
          return;
        }

        installingWorker.onstatechange = () => {
          if (installingWorker.state === "installed") {
            if (navigator.serviceWorker.controller) {
              console.log(
                "New content is available and will be used when all tabs for this page are closed."
              );
              if (config && config.onUpdate) {
                config.onUpdate(registration);
              }
            } else {
              console.log("Content is cached for offline use.");
              if (config && config.onSuccess) {
                config.onSuccess(registration);
              }
              if (config && config.onOfflineReady) {
                config.onOfflineReady();
              }
            }
          }
        };
      };
    })
    .catch((error) => {
      console.error("Error during service worker registration:", error);
    });
}

function checkValidServiceWorker(swUrl: string, config?: ServiceWorkerConfig) {
  fetch(swUrl, {
    headers: { "Service-Worker": "script" },
  })
    .then((response) => {
      const contentType = response.headers.get("content-type");
      if (
        response.status === 404 ||
        (contentType != null && contentType.indexOf("javascript") === -1)
      ) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.unregister().then(() => {
            window.location.reload();
          });
        });
      } else {
        registerValidSW(swUrl, config);
      }
    })
    .catch(() => {
      console.log(
        "No internet connection found. App is running in offline mode."
      );
    });
}

export function unregister() {
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error(error.message);
      });
  }
}

// Utility to check if app is running offline
export function isAppOffline(): boolean {
  if (typeof navigator === "undefined") return false;
  return !navigator.onLine;
}

// Utility to show update available notification
export function showUpdateAvailableNotification() {
  if ("Notification" in window && Notification.permission === "granted") {
    new Notification("Update Available", {
      body: "A new version of the portfolio is available. Refresh to update.",
      icon: "/favicon.ico",
    });
  }
}

// Request notification permission
export async function requestNotificationPermission(): Promise<boolean> {
  if ("Notification" in window) {
    const permission = await Notification.requestPermission();
    return permission === "granted";
  }
  return false;
}

// Cache management utilities
export async function clearCache(): Promise<void> {
  if ("caches" in window) {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map((cacheName) => caches.delete(cacheName)));
    console.log("All caches cleared");
  }
}

export async function getCacheSize(): Promise<number> {
  if (
    "caches" in window &&
    "storage" in navigator &&
    "estimate" in navigator.storage
  ) {
    const estimate = await navigator.storage.estimate();
    return estimate.usage || 0;
  }
  return 0;
}

// Performance monitoring
export function measurePerformance() {
  if (typeof window === "undefined" || !("performance" in window)) {
    return;
  }

  window.addEventListener("load", () => {
    const perfData = performance.getEntriesByType(
      "navigation"
    )[0] as PerformanceNavigationTiming;

    const metrics = {
      dns: perfData.domainLookupEnd - perfData.domainLookupStart,
      tcp: perfData.connectEnd - perfData.connectStart,
      ttfb: perfData.responseStart - perfData.requestStart,
      download: perfData.responseEnd - perfData.responseStart,
      dom:
        perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
      total: perfData.loadEventEnd - perfData.navigationStart,
    };

    console.log("Performance Metrics:", metrics);

    // Send to analytics if needed
    if ((window as any).gtag) {
      (window as any).gtag("event", "timing_complete", {
        name: "load",
        value: metrics.total,
      });
    }
  });
}

// Preload critical resources
export function preloadCriticalResources() {
  if (typeof document === "undefined") {
    return;
  }

  const criticalResources = [
    "/src/assets/fonts/jetbrains-mono.woff2",
    // Add other critical resources
  ];

  criticalResources.forEach((resource) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.href = resource;
    link.as = resource.includes(".woff") ? "font" : "script";
    if (link.as === "font") {
      link.crossOrigin = "anonymous";
    }
    document.head.appendChild(link);
  });
}
