import React from "react";
import { motion } from "framer-motion";

const TestBackground: React.FC = () => {
  return (
    <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-black">
      {/* Debug indicator */}
      <div className="absolute top-4 left-4 z-50 bg-green-500 text-white text-sm px-3 py-2 rounded">
        ✅ Test Background Working!
      </div>
      
      {/* Simple animated elements */}
      {[...Array(20)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-white rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            opacity: [0.2, 1, 0.2],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: 2 + Math.random() * 3,
            repeat: Infinity,
            delay: Math.random() * 2,
          }}
        />
      ))}
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30" />
    </div>
  );
};

export default TestBackground;
