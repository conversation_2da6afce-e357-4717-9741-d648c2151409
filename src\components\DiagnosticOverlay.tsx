import React from "react";
import { useTheme } from "../contexts/ThemeContext";
import { useDeviceDetection } from "../hooks/useDeviceDetection";

const DiagnosticOverlay: React.FC = () => {
  const { isDarkMode } = useTheme();
  const deviceInfo = useDeviceDetection();

  return (
    <div className="fixed top-4 right-4 z-[9999] bg-black/80 text-white text-xs p-4 rounded-lg border border-white/20 font-mono max-w-xs">
      <h3 className="text-green-400 font-bold mb-2">🔍 Diagnostic Info</h3>
      
      <div className="space-y-1">
        <div>
          <span className="text-blue-400">Theme:</span> {isDarkMode ? "Dark" : "Light"}
        </div>
        
        <div>
          <span className="text-blue-400">Device:</span> {deviceInfo.isMobile ? "Mobile" : "Desktop"}
        </div>
        
        <div>
          <span className="text-blue-400">Reduced Motion:</span> {deviceInfo.prefersReducedMotion ? "Yes" : "No"}
        </div>
        
        <div>
          <span className="text-blue-400">Low End:</span> {deviceInfo.isLowEndDevice ? "Yes" : "No"}
        </div>
        
        <div>
          <span className="text-blue-400">Screen:</span> {window.innerWidth}x{window.innerHeight}
        </div>
        
        <div>
          <span className="text-blue-400">User Agent:</span> {navigator.userAgent.slice(0, 30)}...
        </div>
      </div>
      
      <div className="mt-3 pt-2 border-t border-white/20">
        <div className="text-yellow-400 font-bold">Background Tests:</div>
        <div className="mt-1">
          <div className="w-4 h-4 bg-red-500 inline-block mr-2"></div>
          <span>Red Test</span>
        </div>
        <div className="mt-1">
          <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-blue-500 inline-block mr-2"></div>
          <span>Gradient Test</span>
        </div>
      </div>
    </div>
  );
};

export default DiagnosticOverlay;
