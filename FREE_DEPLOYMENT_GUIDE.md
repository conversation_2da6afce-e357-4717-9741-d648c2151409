# 🆓 Free Custom Domain Deployment Guide

## 🌟 Best Free Options (Ranked)

### 1. **Vercel (Recommended)**
- **URL**: `akshayjuluri.vercel.app`
- **Features**: Fast, reliable, professional-looking
- **Setup Time**: 5 minutes

### 2. **Netlify**
- **URL**: `akshayjuluri.netlify.app`
- **Features**: Great for forms, easy deployment
- **Setup Time**: 5 minutes

### 3. **GitHub Pages**
- **URL**: `Akshayy67.github.io`
- **Features**: Completely free, GitHub integration
- **Setup Time**: 10 minutes

## 🚀 Quick Deployment Steps

### Option 1: Vercel (Easiest)
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel

# Follow prompts:
# - Link to existing project? No
# - Project name: akshayjuluri-portfolio
# - Directory: ./
# - Override settings? No
```

### Option 2: Netlify (Drag & Drop)
```bash
# Build your project
npm run build

# Go to netlify.com
# Drag the 'dist' folder to the deploy area
# Choose site name: akshayju<PERSON>ri
```

### Option 3: GitHub Pages (Free Forever)
```bash
# Deploy to GitHub Pages
npm run deploy

# Your site will be at: https://Akshayy67.github.io/project
```

## 🎯 Custom Subdomain Names

### Professional Options:
- `akshayjuluri.vercel.app`
- `akshay-portfolio.vercel.app`
- `juluri-dev.vercel.app`
- `akshaybuilds.vercel.app`

### Creative Options:
- `akshay-codes.netlify.app`
- `juluri-tech.netlify.app`
- `portfolio-akshay.vercel.app`

## 🆓 Completely Free Domain Options

### Freenom (Free TLD)
1. Go to freenom.com
2. Search for: `akshayjuluri`
3. Choose: `.tk`, `.ml`, `.ga`, or `.cf`
4. Register for free (1 year)

**Example**: `akshayjuluri.tk`

### Student Programs (If You're a Student)
1. **GitHub Student Pack**: Free .me domain
2. **Namecheap Education**: Free .me domain
3. **Domain.com Student**: Discounted domains

## 🔧 Setup Instructions

### For Vercel:
1. Go to vercel.com
2. Sign up with GitHub
3. Import your repository
4. Deploy automatically
5. Custom subdomain: Project Settings → Domains

### For Netlify:
1. Build: `npm run build`
2. Go to netlify.com
3. Drag `dist` folder
4. Site Settings → Change site name

### For GitHub Pages:
1. Push code to GitHub
2. Run: `npm run deploy`
3. GitHub Settings → Pages → Source: gh-pages branch

## 📊 Comparison

| Platform | URL Format | Speed | Features | Ease |
|----------|------------|-------|----------|------|
| Vercel | `.vercel.app` | ⭐⭐⭐⭐⭐ | Analytics, Edge | ⭐⭐⭐⭐⭐ |
| Netlify | `.netlify.app` | ⭐⭐⭐⭐ | Forms, Functions | ⭐⭐⭐⭐ |
| GitHub Pages | `.github.io` | ⭐⭐⭐ | Git Integration | ⭐⭐⭐ |
| Freenom | `.tk/.ml/.ga/.cf` | ⭐⭐ | Custom Domain | ⭐⭐ |

## 🎉 Recommended Action Plan

### Immediate (5 minutes):
1. **Deploy to Vercel** for `akshayjuluri.vercel.app`
2. **Test everything works**
3. **Share your portfolio!**

### Later (Optional):
1. **Get student domain** if eligible
2. **Upgrade to paid domain** when ready ($8-12/year)
3. **Add custom analytics**

## 🔗 Environment Variables

Don't forget to add these in your deployment platform:
```env
VITE_EMAILJS_SERVICE_ID=service_mshm1ku
VITE_EMAILJS_TEMPLATE_ID=template_gjtmaxv
VITE_EMAILJS_PUBLIC_KEY=e8z9fkbuNRmd04-70
```

## 💡 Pro Tips

- **Vercel subdomains** look most professional
- **GitHub Pages** is completely free forever
- **Netlify** has great form handling
- **Freenom domains** can be unreliable
- **Student programs** offer best value

Your portfolio is ready to deploy! Choose your preferred option and you'll have a live website in minutes! 🚀
